"use client";

import { DtcSidebar } from "@/components/ui/navigation/DtcSidebar";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-[--color-muted] flex">
      {/* Sidebar */}
      <DtcSidebar />
      
      {/* Main Content */}
      <main className="flex-1 lg:ml-0">
        <div className="p-6 lg:p-8 max-w-7xl mx-auto">
          {children}
        </div>
      </main>
    </div>
  );
}
