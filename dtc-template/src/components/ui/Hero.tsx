"use client";

import Image from "next/image";
import { cn } from "@/lib/utils";
import { dtcAssets } from "@/lib/assets";

interface HeroProps {
  title?: string;
  subtitle?: string;
  description?: string;
  backgroundImage?: keyof typeof dtcAssets;
  className?: string;
  textPosition?: "left" | "center" | "right";
  overlay?: boolean;
  children?: React.ReactNode;
}

export function Hero({
  title,
  subtitle,
  description,
  backgroundImage = "hero1",
  className,
  textPosition = "left",
  overlay = true,
  children,
}: HeroProps) {
  return (
    <div className={cn("relative w-full h-64 lg:h-80 overflow-hidden rounded-2xl", className)}>
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src={dtcAssets[backgroundImage]}
          alt="Hero Background"
          fill
          className="object-cover"
          priority
        />
        {/* Overlay */}
        {overlay && (
          <div className="absolute inset-0 bg-gradient-to-r from-[--color-primary]/80 via-[--color-primary]/60 to-[--color-primary-deep]/70" />
        )}
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className={cn(
          "w-full px-8 lg:px-12",
          textPosition === "center" && "text-center",
          textPosition === "right" && "text-right"
        )}>
          {subtitle && (
            <p className="text-[--color-accent] font-medium text-sm lg:text-base mb-2 tracking-wide uppercase">
              {subtitle}
            </p>
          )}
          
          {title && (
            <h1 className="text-white font-display font-bold text-3xl lg:text-5xl xl:text-6xl leading-tight mb-4">
              {title}
            </h1>
          )}
          
          {description && (
            <p className="text-white/90 text-base lg:text-lg max-w-2xl leading-relaxed">
              {description}
            </p>
          )}
          
          {children && (
            <div className="mt-6">
              {children}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Preset hero variants for common use cases
export function DashboardHero() {
  return (
    <Hero
      subtitle="Welcome to"
      title="DTC Home"
      description="Manage your digital transformation journey with powerful tools and insights."
      backgroundImage="hero1"
    />
  );
}

export function UserManagementHero() {
  return (
    <Hero
      subtitle="Administration"
      title="User Management"
      description="Manage user accounts, roles, and permissions across your organization."
      backgroundImage="hero2"
    />
  );
}
